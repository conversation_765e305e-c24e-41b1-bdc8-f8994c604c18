import axios from 'axios';

const apiClient = axios.create({
    // baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.tclaccord.com',
    baseURL: "https://subcription.tclaccord.com/api",
    headers: {
        'Content-Type': 'application/json',
    },
    timeout: 5000, // 5 second timeout
});

apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

export default apiClient;
