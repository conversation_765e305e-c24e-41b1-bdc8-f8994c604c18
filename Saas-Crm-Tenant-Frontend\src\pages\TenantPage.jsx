import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function TenantPage() {
    const [tenants, setTenants] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentTenant, setCurrentTenant] = useState(null);

    // Fetch tenants
    useEffect(() => {
        apiClient.get('/tenants')
            .then(res => setTenants(res.data.data || res.data))
            .catch(err => toast.error(err.response?.data?.message || err.message));
    }, []);

    // Add / Update Tenant
    const handleFormSubmit = async (formData) => {
        if (!formData.name || !formData.subdomain || !formData.subscription_id || !formData.license_id) {
            toast.error("All fields are required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            if (isUpdate) {
                const payload = {
                    name: formData.name,
                    subdomain: formData.subdomain,
                    subscription_id: formData.subscription_id,
                    license_id: formData.license_id,
                    status: formData.status,
                };

                const { data: updatedTenant } = await apiClient.patch(
                    `/tenants/${formData.id}`,
                    payload
                );

                setTenants((prev) =>
                    prev.map((t) => (t.id === formData.id ? { ...updatedTenant } : t))
                );
                toast.success("Tenant updated successfully!");
            } else {
                const payload = {
                    name: formData.name,
                    subdomain: formData.subdomain,
                    subscription_id: formData.subscription_id,
                    license_id: formData.license_id,
                };

                const { data: newTenant } = await apiClient.post("/tenants", payload);
                setTenants((prev) => [newTenant, ...prev]);
                toast.success("Tenant created successfully!");
            }
            setIsModalOpen(false);
            setCurrentTenant(null);
        } catch (err) {
            toast.error(err.response?.data?.message || `Failed to ${isUpdate ? "update" : "create"} tenant`);
        } finally {
            setFormLoading(false);
        }
    };


    // Delete Tenant
    const handleDeleteTenant = async (tenantId) => {
        try {
            await apiClient.delete(`/tenants/${tenantId}`);
            setTenants((prev) => prev.filter((t) => t.id !== tenantId));
            toast.success("Tenant deleted successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to delete tenant");
        }
    };

    const handleAddTenant = () => {
        setCurrentTenant(null);
        setIsModalOpen(true);
    };

    const handleEditTenant = (tenant) => {
        setCurrentTenant(tenant);
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Tenant Management</CardTitle>
                <Button onClick={handleAddTenant}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Tenant
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Subdomain</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Subscription ID</TableHead>
                            <TableHead>License ID</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : tenants.length > 0 ? (
                            tenants.map(tenant => (
                                <TableRow key={tenant.id}>
                                    <TableCell className="font-medium">{tenant.name}</TableCell>
                                    <TableCell>{tenant.subdomain}</TableCell>
                                    <TableCell>{tenant.status}</TableCell>
                                    <TableCell>{tenant.subscription_id}</TableCell>
                                    <TableCell>{tenant.license_id}</TableCell>
                                    <TableCell>{new Date(tenant.created_at).toLocaleString()}</TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditTenant(tenant)}
                                            title="Edit Tenant"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    title="Delete Tenant"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be undone. This will permanently delete the tenant "{tenant.name}".
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteTenant(tenant.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">No tenants found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Tenant Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentTenant ? "Edit Tenant" : "Create Tenant"}</DialogTitle>
                    </DialogHeader>
                    <TenantForm
                        initialData={currentTenant}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function TenantForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || {
        name: '',
        subdomain: '',
        subscription_id: '',
        license_id: '',
        status: 'active',
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g. Beta Solutions"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="subdomain">Subdomain</Label>
                <Input
                    id="subdomain"
                    type="text"
                    value={formData.subdomain}
                    onChange={handleInputChange}
                    placeholder="e.g. beta"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="subscription_id">Subscription ID</Label>
                <Input
                    id="subscription_id"
                    type="text"
                    value={formData.subscription_id}
                    onChange={handleInputChange}
                    placeholder="e.g. e7f8a9b0-1234-4cde-8f9a-abcdef123456"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="license_id">License ID</Label>
                <Input
                    id="license_id"
                    type="text"
                    value={formData.license_id}
                    onChange={handleInputChange}
                    placeholder="e.g. f1e2d3c4-5678-4b9a-8cde-abcdef654321"
                />
            </div>
            {initialData && (
                <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                        id="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="w-full border rounded p-2"
                    >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            )}
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Tenant" : "Create Tenant"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
